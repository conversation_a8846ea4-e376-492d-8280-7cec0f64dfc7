# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProjectPRDocumentWizard(models.TransientModel):
    _name = 'project.pr.document.wizard'
    _description = 'PRD Document Viewer Wizard'

    project_id = fields.Many2one('project.project', string='Project', required=True)
    pr_document_ids = fields.Many2many('ir.attachment', string='PR Documents')
    selected_document_id = fields.Many2one('ir.attachment', string='Selected Document')
    document_name = fields.Char(string='Document Name', related='selected_document_id.name')
    document_url = fields.Char(string='Document URL', compute='_compute_document_url')
    document_mimetype = fields.Char(string='Document Type', related='selected_document_id.mimetype')
    is_image = fields.Boolean(string='Is Image', compute='_compute_is_image')
    is_pdf = fields.Boolean(string='Is PDF', compute='_compute_is_pdf')

    @api.depends('selected_document_id')
    def _compute_document_url(self):
        for wizard in self:
            if wizard.selected_document_id:
                wizard.document_url = f'/web/content/{wizard.selected_document_id.id}?download=true'
            else:
                wizard.document_url = False

    @api.depends('document_mimetype')
    def _compute_is_image(self):
        for wizard in self:
            wizard.is_image = wizard.document_mimetype and wizard.document_mimetype.startswith('image/')

    @api.depends('document_mimetype')
    def _compute_is_pdf(self):
        for wizard in self:
            wizard.is_pdf = wizard.document_mimetype == 'application/pdf'

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        project_id = self.env.context.get('active_id')
        if project_id:
            project = self.env['project.project'].browse(project_id)
            res['project_id'] = project_id
            res['pr_document_ids'] = [(6, 0, project.pr_document.ids)]
            if project.pr_document:
                res['selected_document_id'] = project.pr_document[0].id
        return res

    def action_download_document(self):
        """Download the selected document"""
        if self.selected_document_id:
            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{self.selected_document_id.id}?download=true',
                'target': 'new',
            }

    def action_view_document(self):
        """View the document in a new tab"""
        if self.selected_document_id:
            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{self.selected_document_id.id}',
                'target': 'new',
            }
