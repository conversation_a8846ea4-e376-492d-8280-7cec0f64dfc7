<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- PRD Document Wizard Form View -->
    <record id="view_project_pr_document_wizard_form" model="ir.ui.view">
        <field name="name">project.pr.document.wizard.form</field>
        <field name="model">project.pr.document.wizard</field>
        <field name="arch" type="xml">
            <form string="PRD Document Viewer">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="project_id" readonly="1" options="{'no_open': True}"/>
                        </h1>
                        <h2>PRD Document Viewer</h2>
                    </div>
                    
                    <group>
                        <group>
                            <field name="pr_document_ids" invisible="1"/>
                            <field name="selected_document_id" 
                                   domain="[('id', 'in', pr_document_ids)]"
                                   options="{'no_create': True, 'no_edit': True}"
                                   attrs="{'invisible': [('pr_document_ids', '=', [])]}"/>
                            <field name="document_name" readonly="1" 
                                   attrs="{'invisible': [('selected_document_id', '=', False)]}"/>
                            <field name="document_mimetype" readonly="1" 
                                   attrs="{'invisible': [('selected_document_id', '=', False)]}"/>
                            <field name="is_image" invisible="1"/>
                            <field name="is_pdf" invisible="1"/>
                        </group>
                    </group>

                    <!-- Document Preview Section -->
                    <div attrs="{'invisible': [('selected_document_id', '=', False)]}">
                        <separator string="Document Preview"/>
                        
                        <!-- Image Preview -->
                        <div attrs="{'invisible': [('is_image', '=', False)]}" class="text-center">
                            <field name="selected_document_id" widget="image"
                                   options="{'size': [800, 600]}"
                                   attrs="{'invisible': [('selected_document_id', '=', False)]}"/>
                        </div>

                        <!-- PDF Preview -->
                        <div attrs="{'invisible': [('is_pdf', '=', False)]}" class="text-center">
                            <div class="alert alert-info">
                                <i class="fa fa-file-pdf-o"/>
                                PDF Preview - Click "View in New Tab" to see the full document.
                            </div>
                        </div>
                        
                        <!-- Other file types -->
                        <div attrs="{'invisible': ['|', ('is_image', '=', True), ('is_pdf', '=', True)]}" 
                             class="text-center">
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"/> 
                                This file type cannot be previewed. Please download to view.
                            </div>
                        </div>
                    </div>

                    <!-- No documents message -->
                    <div attrs="{'invisible': [('pr_document_ids', '!=', [])]}" class="text-center">
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"/> 
                            No PRD documents found for this project.
                        </div>
                    </div>
                </sheet>
                
                <footer>
                    <button string="Download" 
                            name="action_download_document" 
                            type="object" 
                            class="btn-primary"
                            attrs="{'invisible': [('selected_document_id', '=', False)]}"/>
                    <button string="View in New Tab" 
                            name="action_view_document" 
                            type="object" 
                            class="btn-secondary"
                            attrs="{'invisible': [('selected_document_id', '=', False)]}"/>
                    <button string="Close" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- PRD Document Wizard Action -->
    <record id="action_project_pr_document_wizard" model="ir.actions.act_window">
        <field name="name">PRD Document Viewer</field>
        <field name="res_model">project.pr.document.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_project_pr_document_wizard_form"/>
        <field name="target">new</field>
        <field name="context">{'active_id': active_id}</field>
    </record>
</odoo>
